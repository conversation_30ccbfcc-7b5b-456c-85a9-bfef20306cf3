# Ignore everything by default
*

# Allow specific files and directories
!package.json
!package-lock.json
!turbo.json
!apps/
!packages/

# Ignore node_modules everywhere
**/node_modules
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# Ignore build outputs
**/dist
**/build
**/.next
**/coverage

# Ignore development files
**/.env.local
**/.env.development.local
**/.env.test.local
**/.env.production.local

# Ignore IDE files
**/.vscode
**/.idea
**/*.swp
**/*.swo

# Ignore OS files
**/.DS_Store
**/Thumbs.db

# Ignore Docker files
**/Dockerfile*
**/docker-compose*

# Ignore git
**/.git
**/.gitignore

# Ignore documentation
**/README.md
**/CHANGELOG.md

# Ignore test files
**/*.test.*
**/*.spec.*
**/test/
**/tests/

# Ignore temporary files
**/tmp/
**/temp/
**/*.tmp
**/*.temp

# Ignore TurboRepo cache
**/.turbo
