{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "apps/www/Dockerfile", "buildContext": ".", "watchPaths": ["apps/www/**", "packages/eslint-config/**", "packages/typescript-config/**", "package.json", "package-lock.json", "turbo.json"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "startCommand": "npm run start", "healthcheckPath": "/", "healthcheckTimeout": 100}}