# Optimized Dockerfile for TurboRepo monorepo deployment
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install turbo globally
RUN npm install -g turbo@^2

# Copy the entire monorepo
COPY . .

# Generate a partial monorepo with a pruned lockfile for the www workspace
RUN turbo prune www --docker

# Install dependencies stage
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN npm ci

# Copy source code and build
COPY --from=builder /app/out/full/ .
RUN npm install turbo@^2
RUN npx turbo run build --filter=www

# Production runtime stage
FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 reactrouter

# Copy built application and dependencies
COPY --from=installer --chown=reactrouter:nodejs /app/apps/www/build ./build
COPY --from=installer --chown=reactrouter:nodejs /app/apps/www/package.json ./package.json
COPY --from=installer --chown=reactrouter:nodejs /app/node_modules ./node_modules

USER reactrouter

EXPOSE 3000

ENV PORT=3000
ENV NODE_ENV=production

CMD ["npm", "run", "start"]