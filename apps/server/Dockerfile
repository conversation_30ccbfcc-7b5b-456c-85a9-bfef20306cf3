# Optimized Dockerfile for TurboRepo monorepo deployment
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install turbo globally
RUN npm install -g turbo@^2

# Copy the entire monorepo
COPY . .

# Generate a partial monorepo with a pruned lockfile for the server workspace
RUN turbo prune server --docker

# Install dependencies stage
FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
RUN npm ci --only=production

# Copy source code and build
COPY --from=builder /app/out/full/ .
RUN npm install turbo@^2
RUN npx turbo run build --filter=server

# Production runtime stage
FROM base AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Copy built application and dependencies
COPY --from=installer --chown=nestjs:nodejs /app/apps/server/dist ./dist
COPY --from=installer --chown=nestjs:nodejs /app/apps/server/package.json ./package.json
COPY --from=installer --chown=nestjs:nodejs /app/node_modules ./node_modules

USER nestjs

EXPOSE 3000

ENV PORT=3000
ENV NODE_ENV=production

CMD ["node", "dist/main.js"]
