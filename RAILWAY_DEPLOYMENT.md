# Railway Deployment Guide for TurboRepo Monorepo

This guide explains how to deploy your TurboRepo monorepo with NestJS server and React Router frontend to Railway.

## Deployment Strategy

We're using **separate services** deployment strategy:
- Each app (server, www) deploys as an independent Railway service
- Shared packages are included in each build using TurboRepo's `turbo prune`
- Optimized Docker builds with proper layer caching

## Prerequisites

1. Railway account and CLI installed
2. GitHub repository connected to Railway
3. TurboRepo v2+ installed globally

## Deployment Steps

### 1. Connect Repository to Railway

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create a new project
railway new
```

### 2. Deploy Server Service

```bash
# From the repository root
railway service create server

# Link to the server service
railway link --service server

# Set the railway.json path for server
# Railway will automatically detect apps/server/railway.json

# Deploy
railway up
```

### 3. Deploy Frontend Service

```bash
# Create frontend service
railway service create www

# Link to the www service
railway link --service www

# Deploy
railway up
```

## Configuration Details

### Docker Optimization

Both Dockerfiles use TurboRepo's `turbo prune` feature:

1. **Pruning**: Only includes dependencies needed for each app
2. **Layer Caching**: Separates dependency installation from source code
3. **Multi-stage**: Optimized for production with minimal runtime image

### Watch Paths

Each service only rebuilds when relevant files change:

**Server Service:**
- `apps/server/**`
- `packages/eslint-config/**`
- `packages/typescript-config/**`
- `package.json`
- `package-lock.json`
- `turbo.json`

**Frontend Service:**
- `apps/www/**`
- `packages/eslint-config/**`
- `packages/typescript-config/**`
- `package.json`
- `package-lock.json`
- `turbo.json`

## Environment Variables

Set these in Railway dashboard for each service:

### Server Service
```
NODE_ENV=production
PORT=3000
```

### Frontend Service
```
NODE_ENV=production
PORT=3000
VITE_API_URL=https://your-server-service.railway.app
```

## Monitoring

Both services include:
- Health checks on `/` endpoint
- Automatic restarts on failure
- Resource monitoring in Railway dashboard

## Troubleshooting

### Build Issues
1. Check TurboRepo version compatibility
2. Verify shared package dependencies
3. Review Docker build logs

### Runtime Issues
1. Check environment variables
2. Verify service connectivity
3. Review application logs in Railway dashboard

## Cost Optimization

- Services scale independently based on usage
- Shared packages don't duplicate costs
- Efficient Docker caching reduces build times
- Watch paths prevent unnecessary rebuilds

## Next Steps

1. Set up custom domains
2. Configure environment-specific variables
3. Set up monitoring and alerts
4. Consider adding a database service if needed
